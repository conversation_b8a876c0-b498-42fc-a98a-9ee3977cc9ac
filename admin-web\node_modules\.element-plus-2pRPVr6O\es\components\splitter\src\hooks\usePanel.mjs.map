{"version": 3, "file": "usePanel.mjs", "sources": ["../../../../../../../packages/components/splitter/src/hooks/usePanel.ts"], "sourcesContent": ["import { isObject } from '@element-plus/utils'\n\nimport type { PanelItemState } from '../type'\n\nexport function getCollapsible(\n  collapsible: boolean | { start?: boolean; end?: boolean }\n) {\n  if (collapsible && isObject(collapsible)) {\n    return collapsible\n  }\n  return {\n    start: !!collapsible,\n    end: !!collapsible,\n  }\n}\n\nexport function isCollapsible(\n  panel: PanelItemState | null | undefined,\n  size: number,\n  nextPanel: PanelItemState | null | undefined,\n  nextSize: number\n) {\n  // If the current panel is collapsible and has size, then it can be collapsed\n  if (panel?.collapsible.end && size > 0) {\n    return true\n  }\n\n  // If the next panel is collapsible and has no size, but the current panel has size, then it can be collapsed\n  if (nextPanel?.collapsible.start && nextSize === 0 && size > 0) {\n    return true\n  }\n\n  return false\n}\n"], "names": [], "mappings": ";;AACO,SAAS,cAAc,CAAC,WAAW,EAAE;AAC5C,EAAE,IAAI,WAAW,IAAI,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC5C,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG;AACH,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,CAAC,CAAC,WAAW;AACxB,IAAI,GAAG,EAAE,CAAC,CAAC,WAAW;AACtB,GAAG,CAAC;AACJ,CAAC;AACM,SAAS,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE;AAChE,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,EAAE;AACpE,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,KAAK,QAAQ,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE;AAChG,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf;;;;"}