{"version": 3, "file": "cell.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/components/cell.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\n\nimport type { FunctionalComponent } from 'vue'\nimport type { TableV2CellProps } from '../cell'\n\nconst TableV2Cell: FunctionalComponent<TableV2CellProps> = (\n  props: TableV2CellProps,\n  { slots }\n) => {\n  const { cellData, style } = props\n  const displayText = cellData?.toString?.() || ''\n  const defaultSlot = renderSlot(slots, 'default', props, () => [displayText])\n  return (\n    <div class={props.class} title={displayText} style={style}>\n      {defaultSlot}\n    </div>\n  )\n}\n\nTableV2Cell.displayName = 'ElTableV2Cell'\nTableV2Cell.inheritAttrs = false\n\nexport default TableV2Cell\n"], "names": ["slots", "cellData", "style", "props", "displayText", "toString", "defaultSlot", "renderSlot", "_createVNode", "class", "TableV2Cell", "displayName"], "mappings": ";;;AAKA,EAAA,KAAwD;AAEpDA,CAAAA,KAAAA;AAAF,EACG,IAAA,EAAA,CAAA;EACH,MAAM;IAAEC,QAAF;AAAYC,IAAAA,KAAAA;AAAZ,GAAA,GAAsBC,KAA5B,CAAA;AACA,EAAA,MAAMC,WAAW,GAAGH,CAAAA,CAAAA,EAAAA,GAAAA,QAAUI,WAAgB,KAA9C,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,CAAA,KAAA,EAAA,CAAA;AACA,EAAA,MAAMC,WAAW,GAAGC,UAAU,CAACP,KAAD,EAAQ,SAAR,EAAmBG,KAAnB,EAA0B,MAAM,CAACC,WAAD,CAAhC,CAA9B,CAAA;AACA,EAAA,OAAAI,WAAA,CAAA,KAAA,EAAA;IAAA,OACcL,EAAAA,KAAK,CAACM,KADpB;AAAA,IAAA,OAAA,EACkCL,WADlC;IAAA,OACsDF,EAAAA,KAAAA;AADtD,GAAA,EAAA,CAEKI,WAFL,CAAA,CAAA,CAAA;AAKD,CAZD,CAAA;;AAcAI,WAAW,CAACC,YAAZ,GAAA,KAAA,CAAA;AACAD,gBAAA,WAAA;;;;"}