{"version": 3, "file": "link2.js", "sources": ["../../../../../../packages/components/link/src/link.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, PropType, __ExtractPublicPropTypes } from 'vue'\nimport type Link from './link.vue'\n\nexport const linkProps = buildProps({\n  /**\n   * @description type\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'warning', 'info', 'danger', 'default'],\n    default: undefined,\n  },\n  /**\n   * @description when underlines should appear\n   */\n  underline: {\n    type: [Boolean, String],\n    values: [true, false, 'always', 'never', 'hover'],\n    default: undefined,\n  },\n  /**\n   * @description whether the component is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description same as native hyperlink's `href`\n   */\n  href: { type: String, default: '' },\n  /**\n   * @description same as native hyperlink's `target`\n   */\n  target: {\n    type: String as PropType<'_blank' | '_parent' | '_self' | '_top' | string>,\n    default: '_self',\n  },\n  /**\n   * @description icon component\n   */\n  icon: {\n    type: iconPropType,\n  },\n} as const)\nexport type LinkProps = ExtractPropTypes<typeof linkProps>\nexport type LinkPropsPublic = __ExtractPublicPropTypes<typeof linkProps>\n\nexport const linkEmits = {\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\nexport type LinkEmits = typeof linkEmits\n\nexport type LinkInstance = InstanceType<typeof Link> & unknown\nexport interface LinkConfigContext {\n  type?: string\n  underline?: string | boolean\n}\n"], "names": ["buildProps", "iconPropType"], "mappings": ";;;;;;;AACY,MAAC,SAAS,GAAGA,kBAAU,CAAC;AACpC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;AAC1E,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC3B,IAAI,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;AACrD,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE;AACrC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,iBAAY;AACtB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,SAAS,GAAG;AACzB,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C;;;;;"}