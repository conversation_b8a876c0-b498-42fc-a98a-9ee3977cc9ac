{"version": 3, "file": "menu-collapse-transition.js", "sources": ["../../../../../../packages/components/menu/src/menu-collapse-transition.vue"], "sourcesContent": ["<template>\n  <transition mode=\"out-in\" v-bind=\"listeners\">\n    <slot />\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport { addClass, hasClass, removeClass } from '@element-plus/utils'\n\nimport type { BaseTransitionProps, TransitionProps } from 'vue'\n\ndefineOptions({\n  name: 'ElMenuCollapseTransition',\n})\n\nconst ns = useNamespace('menu')\nconst listeners = {\n  onBeforeEnter: (el) => (el.style.opacity = '0.2'),\n  onEnter(el, done) {\n    addClass(el, `${ns.namespace.value}-opacity-transition`)\n    el.style.opacity = '1'\n    done()\n  },\n\n  onAfterEnter(el) {\n    removeClass(el, `${ns.namespace.value}-opacity-transition`)\n    el.style.opacity = ''\n  },\n\n  onBeforeLeave(el) {\n    if (!el.dataset) (el as any).dataset = {}\n\n    if (hasClass(el, ns.m('collapse'))) {\n      removeClass(el, ns.m('collapse'))\n      el.dataset.oldOverflow = el.style.overflow\n      el.dataset.scrollWidth = el.clientWidth.toString()\n      addClass(el, ns.m('collapse'))\n    } else {\n      addClass(el, ns.m('collapse'))\n      el.dataset.oldOverflow = el.style.overflow\n      el.dataset.scrollWidth = el.clientWidth.toString()\n      removeClass(el, ns.m('collapse'))\n    }\n\n    el.style.width = `${el.scrollWidth}px`\n    el.style.overflow = 'hidden'\n  },\n\n  onLeave(el: HTMLElement) {\n    addClass(el, 'horizontal-collapse-transition')\n    el.style.width = `${el.dataset.scrollWidth}px`\n  },\n} as BaseTransitionProps<HTMLElement> as TransitionProps\n</script>\n"], "names": ["useNamespace", "addClass", "removeClass", "hasClass"], "mappings": ";;;;;;;;;uCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,0BAAA;AACR,CAAA,CAAA,CAAA;;;;AAEA,IAAM,MAAA,EAAA,GAAKA,mBAAa,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAM,SAAY,GAAA;AAAA,MAChB,aAAe,EAAA,CAAC,EAAQ,KAAA,EAAA,CAAG,MAAM,OAAU,GAAA,KAAA;AAAA,MAC3C,OAAA,CAAQ,IAAI,IAAM,EAAA;AAChB,QAAAC,cAAA,CAAS,EAAI,EAAA,CAAA,EAAG,EAAG,CAAA,SAAA,CAAU,KAAK,CAAqB,mBAAA,CAAA,CAAA,CAAA;AACvD,QAAA,EAAA,CAAG,MAAM,OAAU,GAAA,GAAA,CAAA;AACnB,QAAK,IAAA,EAAA,CAAA;AAAA,OACP;AAAA,MAEA,aAAa,EAAI,EAAA;AACf,QAAAC,iBAAA,CAAY,EAAI,EAAA,CAAA,EAAG,EAAG,CAAA,SAAA,CAAU,KAAK,CAAqB,mBAAA,CAAA,CAAA,CAAA;AAC1D,QAAA,EAAA,CAAG,MAAM,OAAU,GAAA,EAAA,CAAA;AAAA,OACrB;AAAA,MAEA,cAAc,EAAI,EAAA;AAChB,QAAA,IAAI,CAAC,EAAG,CAAA,OAAA;AAER,UAAA,EAAI,WAAa,EAAA,CAAA;AACf,QAAA,IAAAC,cAAA,CAAA,EAAgB,EAAA,EAAA,CAAA,CAAG,CAAE,UAAA,CAAU,CAAC,EAAA;AAChC,UAAGD,iBAAQ,CAAc,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,UAAS,CAAA,CAAA,CAAA;AAClC,UAAA,EAAA,CAAG,OAAQ,CAAA,WAAA,GAAc,EAAG,CAAA,KAAA,CAAA,QAAqB,CAAA;AACjD,UAAA,EAAA,CAAA,OAAa,CAAA,WAAK,GAAA,EAAA,CAAU,WAAC,CAAA,QAAA,EAAA,CAAA;AAAA,UACxBD,cAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AACL,SAAA,MAAA;AACA,UAAGA,cAAA,CAAA,EAAQ,EAAc,EAAA,CAAA,CAAA,CAAA,UAAG,CAAM,CAAA,CAAA;AAClC,UAAA,EAAA,CAAG,OAAQ,CAAA,WAAA,GAAc,EAAG,CAAA,KAAA,CAAA,QAAqB,CAAA;AACjD,UAAA,EAAA,CAAA,OAAA,CAAA,WAAqB,GAAA,EAAA,CAAA,WAAW,CAAA,QAAA,EAAA,CAAA;AAAA,UAClCC,iBAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAEA,SAAA;AACA,QAAA,EAAA,CAAG,MAAM,KAAW,GAAA,CAAA,EAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAAA,QACtB,EAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA,CAAA;AAAA;AAGE,MAAA,OAAA,CAAA,EAAA;AACA,QAAAD,cAAS,CAAA,EAAA,EAAA,gCAAiC,CAAA,CAAA;AAAA,QAC5C,EAAA,CAAA,KAAA,CAAA,KAAA,GAAA,CAAA,EAAA,EAAA,CAAA,OAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAAA,OACF;;;;;;;;;;;;;;;;"}