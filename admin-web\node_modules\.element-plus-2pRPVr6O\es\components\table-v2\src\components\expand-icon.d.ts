import type { StyleValue } from 'vue';
import type { TableV2RowCellRenderParam } from './row';
declare const ExpandIcon: (props: TableV2RowCellRenderParam["expandIconProps"] & {
    class?: string | string[];
    style: StyleValue;
    size: number;
    expanded: boolean;
    expandable: boolean;
}) => JSX.Element;
export default ExpandIcon;
export type ExpandIconInstance = ReturnType<typeof ExpandIcon>;
