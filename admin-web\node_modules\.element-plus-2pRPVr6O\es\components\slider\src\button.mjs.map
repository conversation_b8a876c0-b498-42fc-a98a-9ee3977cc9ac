{"version": 3, "file": "button.mjs", "sources": ["../../../../../../packages/components/slider/src/button.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport { buildProps, isNumber } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type {\n  ComponentPublicInstance,\n  ExtractPropTypes,\n  Ref,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type Button from './button.vue'\n\nexport const sliderButtonProps = buildProps({\n  modelValue: {\n    type: Number,\n    default: 0,\n  },\n  vertical: Boolean,\n  tooltipClass: String,\n  placement: {\n    type: String,\n    values: placements,\n    default: 'top',\n  },\n} as const)\nexport type SliderButtonProps = ExtractPropTypes<typeof sliderButtonProps>\nexport type SliderButtonPropsPublic = __ExtractPublicPropTypes<\n  typeof sliderButtonProps\n>\n\nexport const sliderButtonEmits = {\n  [UPDATE_MODEL_EVENT]: (value: number) => isNumber(value),\n}\nexport type SliderButtonEmits = typeof sliderButtonEmits\n\nexport type SliderButtonInstance = ComponentPublicInstance<typeof Button>\n\nexport type ButtonRefs = Record<\n  'firstButton' | 'secondButton',\n  Ref<SliderButtonInstance | undefined>\n>\n\nexport interface SliderButtonInitData {\n  hovering: boolean\n  dragging: boolean\n  isClick: boolean\n  startX: number\n  currentX: number\n  startY: number\n  currentY: number\n  startPosition: number\n  newPosition: number\n  oldValue: number\n}\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,YAAY,EAAE,MAAM;AACtB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,iBAAiB,GAAG;AACjC,EAAE,CAAC,kBAAkB,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC;AAClD;;;;"}