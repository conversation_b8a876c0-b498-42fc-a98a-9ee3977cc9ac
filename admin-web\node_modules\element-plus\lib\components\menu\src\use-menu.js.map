{"version": 3, "file": "use-menu.js", "sources": ["../../../../../../packages/components/menu/src/use-menu.ts"], "sourcesContent": ["import { computed } from 'vue'\n\nimport type { ComponentInternalInstance, Ref } from 'vue'\n\nexport default function useMenu(\n  instance: ComponentInternalInstance,\n  currentIndex: Ref<string>\n) {\n  const indexPath = computed(() => {\n    let parent = instance.parent!\n    const path = [currentIndex.value]\n    while (parent.type.name !== 'ElMenu') {\n      if (parent.props.index) {\n        path.unshift(parent.props.index as string)\n      }\n      parent = parent.parent!\n    }\n    return path\n  })\n\n  const parentMenu = computed(() => {\n    let parent = instance.parent\n    while (parent && !['ElMenu', 'ElSubMenu'].includes(parent.type.name!)) {\n      parent = parent.parent\n    }\n    return parent!\n  })\n\n  return {\n    parentMenu,\n    indexPath,\n  }\n}\n"], "names": ["computed"], "mappings": ";;;;;;AACe,SAAS,OAAO,CAAC,QAAQ,EAAE,YAAY,EAAE;AACxD,EAAE,MAAM,SAAS,GAAGA,YAAQ,CAAC,MAAM;AACnC,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACjC,IAAI,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACtC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC1C,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE;AAC9B,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACzC,OAAO;AACP,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM;AACpC,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACjC,IAAI,OAAO,MAAM,IAAI,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC1E,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,SAAS;AACb,GAAG,CAAC;AACJ;;;;"}