{"version": 3, "file": "collection2.js", "sources": ["../../../../../../packages/components/collection/src/collection.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\" setup>\ndefineOptions({\n  inheritAttrs: false,\n})\n</script>\n"], "names": ["DO_defineComponent", "_renderSlot"], "mappings": ";;;;;;;kCAKcA,mBAAA,CAAA;AAAA,EACZ,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;SANEC,cAAQ,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;;;;;;"}