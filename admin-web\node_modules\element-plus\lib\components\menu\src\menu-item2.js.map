{"version": 3, "file": "menu-item2.js", "sources": ["../../../../../../packages/components/menu/src/menu-item.vue"], "sourcesContent": ["<template>\n  <li\n    :class=\"[\n      nsMenuItem.b(),\n      nsMenuItem.is('active', active),\n      nsMenuItem.is('disabled', disabled),\n    ]\"\n    role=\"menuitem\"\n    tabindex=\"-1\"\n    @click=\"handleClick\"\n  >\n    <el-tooltip\n      v-if=\"\n        parentMenu.type.name === 'ElMenu' &&\n        rootMenu.props.collapse &&\n        $slots.title\n      \"\n      :effect=\"rootMenu.props.popperEffect\"\n      placement=\"right\"\n      :fallback-placements=\"['left']\"\n      :persistent=\"rootMenu.props.persistent\"\n    >\n      <template #content>\n        <slot name=\"title\" />\n      </template>\n      <div :class=\"nsMenu.be('tooltip', 'trigger')\">\n        <slot />\n      </div>\n    </el-tooltip>\n    <template v-else>\n      <slot />\n      <slot name=\"title\" />\n    </template>\n  </li>\n</template>\n\n<script lang=\"ts\" setup>\n// @ts-nocheck\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onBeforeUnmount,\n  onMounted,\n  reactive,\n  toRef,\n} from 'vue'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { debugWarn, isPropAbsent, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport useMenu from './use-menu'\nimport { menuItemEmits, menuItemProps } from './menu-item'\nimport { MENU_INJECTION_KEY, SUB_MENU_INJECTION_KEY } from './tokens'\n\nimport type { MenuItemRegistered, MenuProvider, SubMenuProvider } from './types'\n\nconst COMPONENT_NAME = 'ElMenuItem'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(menuItemProps)\nconst emit = defineEmits(menuItemEmits)\n\nisPropAbsent(props.index) &&\n  debugWarn(COMPONENT_NAME, 'Missing required prop: \"index\"')\n\nconst instance = getCurrentInstance()!\nconst rootMenu = inject<MenuProvider>(MENU_INJECTION_KEY)\nconst nsMenu = useNamespace('menu')\nconst nsMenuItem = useNamespace('menu-item')\nif (!rootMenu) throwError(COMPONENT_NAME, 'can not inject root menu')\n\nconst { parentMenu, indexPath } = useMenu(instance, toRef(props, 'index'))\n\nconst subMenu = inject<SubMenuProvider>(\n  `${SUB_MENU_INJECTION_KEY}${parentMenu.value.uid}`\n)\nif (!subMenu) throwError(COMPONENT_NAME, 'can not inject sub menu')\n\nconst active = computed(() => props.index === rootMenu.activeIndex)\nconst item: MenuItemRegistered = reactive({\n  index: props.index,\n  indexPath,\n  active,\n})\n\nconst handleClick = () => {\n  if (!props.disabled) {\n    rootMenu.handleMenuItemClick({\n      index: props.index,\n      indexPath: indexPath.value,\n      route: props.route,\n    })\n    emit('click', item)\n  }\n}\n\nonMounted(() => {\n  subMenu.addSubMenu(item)\n  rootMenu.addMenuItem(item)\n})\n\nonBeforeUnmount(() => {\n  subMenu.removeSubMenu(item)\n  rootMenu.removeMenuItem(item)\n})\n\ndefineExpose({\n  parentMenu,\n  rootMenu,\n  active,\n  nsMenu,\n  nsMenuItem,\n  handleClick,\n})\n</script>\n"], "names": ["isPropAbsent", "debugWarn", "getCurrentInstance", "inject", "MENU_INJECTION_KEY", "useNamespace", "throwError", "useMenu", "toRef", "SUB_MENU_INJECTION_KEY", "computed", "reactive", "onMounted", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;;;uCAyDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAIA,IAAAA,kBAAA,CAAa,KAAM,CAAA,KAAK,CACtB,IAAAC,eAAA,CAAU,gBAAgB,gCAAgC,CAAA,CAAA;AAE5D,IAAA,MAAM,WAAWC,sBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,QAAA,GAAWC,WAAqBC,yBAAkB,CAAA,CAAA;AACxD,IAAM,MAAA,MAAA,GAASC,mBAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,UAAA,GAAaA,mBAAa,WAAW,CAAA,CAAA;AAC3C,IAAA,IAAI,CAAC,QAAA;AAEL,MAAMC,+BAAwB,EAAA,0BAAsB,CAAA,CAAA;AAEpD,IAAA,MAAM,EAAU,UAAA,EAAA,SAAA,EAAA,GAAAC,kBAAA,CAAA,QAAA,EAAAC,SAAA,CAAA,KAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACX,OAAA,GAAAL,UAAA,CAAA,CAAA,EAAAM,6BAA6C,CAAA,EAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IAClD,IAAA,CAAA,OAAA;AACA,MAAAH,gBAAK,CAAS,cAAW,EAAA,yBAAyC,CAAA,CAAA;AAElE,IAAA,MAAM,SAASI,YAAS,CAAA,MAAM,KAAM,CAAA,KAAA,KAAU,SAAS,WAAW,CAAA,CAAA;AAClE,IAAA,MAAM,OAA2BC,YAAS,CAAA;AAAA,MACxC,OAAO,KAAM,CAAA,KAAA;AAAA,MACb,SAAA;AAAA,MACA,MAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAM,cAAc,MAAM;AACxB,MAAI,IAAA,CAAC,MAAM,QAAU,EAAA;AACnB,QAAA,QAAA,CAAS,mBAAoB,CAAA;AAAA,UAC3B,OAAO,KAAM,CAAA,KAAA;AAAA,UACb,WAAW,SAAU,CAAA,KAAA;AAAA,UACrB,OAAO,KAAM,CAAA,KAAA;AAAA,SACd,CAAA,CAAA;AACD,QAAA,IAAA,CAAK,SAAS,IAAI,CAAA,CAAA;AAAA,OACpB;AAAA,KACF,CAAA;AAEA,IAAAC,aAAA,CAAU,MAAM;AACd,MAAA,OAAA,CAAQ,WAAW,IAAI,CAAA,CAAA;AACvB,MAAA,QAAA,CAAS,YAAY,IAAI,CAAA,CAAA;AAAA,KAC1B,CAAA,CAAA;AAED,IAAAC,mBAAA,CAAgB,MAAM;AACpB,MAAA,OAAA,CAAQ,cAAc,IAAI,CAAA,CAAA;AAC1B,MAAA,QAAA,CAAS,eAAe,IAAI,CAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MACX,UAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}