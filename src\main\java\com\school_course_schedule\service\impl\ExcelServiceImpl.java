package com.school_course_schedule.service.impl;

import com.school_course_schedule.entity.*;
import com.school_course_schedule.service.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel导入导出服务实现类
 */
@Service
public class ExcelServiceImpl implements ExcelService {

    @Autowired
    private CourseService courseService;

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private UserService userService;

    @Autowired
    private ExamService examService;

    @Override
    public void exportCourses(List<Course> courses, HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("课程信息");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"课程代码", "课程名称", "教师姓名", "学分", "最大人数", "课程描述"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        for (int i = 0; i < courses.size(); i++) {
            Row row = sheet.createRow(i + 1);
            Course course = courses.get(i);

            row.createCell(0).setCellValue(course.getCode());
            row.createCell(1).setCellValue(course.getName());
            row.createCell(2).setCellValue(course.getTeacherName());
            row.createCell(3).setCellValue(course.getCredits() != null ? course.getCredits().doubleValue() : 0);
            row.createCell(4).setCellValue(course.getMaxStudents() != null ? course.getMaxStudents() : 0);
            row.createCell(5).setCellValue(course.getDescription());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("课程信息.xlsx", "UTF-8"));

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public List<Course> importCourses(MultipartFile file) throws IOException {
        List<Course> courses = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            Course course = new Course();
            course.setCode(getCellStringValue(row.getCell(0)));
            course.setName(getCellStringValue(row.getCell(1)));
            course.setTeacherName(getCellStringValue(row.getCell(2)));

            Cell creditsCell = row.getCell(3);
            if (creditsCell != null) {
                course.setCredits(BigDecimal.valueOf(creditsCell.getNumericCellValue()));
            }

            Cell maxStudentsCell = row.getCell(4);
            if (maxStudentsCell != null) {
                course.setMaxStudents((int) maxStudentsCell.getNumericCellValue());
            }

            course.setDescription(getCellStringValue(row.getCell(5)));
            course.setColor("#4CAF50"); // 默认颜色

            courses.add(course);
        }

        workbook.close();
        return courses;
    }

    @Override
    public void exportSchedules(List<Schedule> schedules, HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("排课信息");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"课程代码", "课程名称", "教室", "星期", "开始时间", "结束时间", "周次", "学期"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        for (int i = 0; i < schedules.size(); i++) {
            Row row = sheet.createRow(i + 1);
            Schedule schedule = schedules.get(i);

            // 这里需要根据实际的关联查询来获取课程和教室信息
            row.createCell(0).setCellValue(""); // 课程代码
            row.createCell(1).setCellValue(""); // 课程名称
            row.createCell(2).setCellValue(""); // 教室
            row.createCell(3).setCellValue(schedule.getDayOfWeek());
            row.createCell(4).setCellValue("时间段-" + schedule.getTimePeriodId());
            row.createCell(5).setCellValue(""); // 结束时间留空
            row.createCell(6).setCellValue(schedule.getWeeks());
            row.createCell(7).setCellValue("学期-" + schedule.getSemesterId());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("排课信息.xlsx", "UTF-8"));

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public List<Schedule> importSchedules(MultipartFile file) throws IOException {
        List<Schedule> schedules = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            Schedule schedule = new Schedule();
            // 这里需要根据课程代码查找课程ID
            // schedule.setCourseId(courseId);
            // 这里需要根据教室名称查找教室ID
            // schedule.setClassroomId(classroomId);

            String dayOfWeekStr = getCellStringValue(row.getCell(3));
            if (dayOfWeekStr != null) {
                schedule.setDayOfWeek(dayOfWeekStr); // 直接设置字符串
            }

            // 设置默认时间段ID，实际应该根据时间解析
            schedule.setTimePeriodId(1L);

            schedule.setWeeks(getCellStringValue(row.getCell(6)));
            schedule.setSemesterId(1L); // 设置默认学期ID

            schedules.add(schedule);
        }

        workbook.close();
        return schedules;
    }

    @Override
    public void exportUsers(List<User> users, HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("用户信息");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"用户名", "工号/学号", "昵称", "邮箱", "手机号", "角色", "部门", "状态"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        for (int i = 0; i < users.size(); i++) {
            Row row = sheet.createRow(i + 1);
            User user = users.get(i);

            row.createCell(0).setCellValue(user.getUsername());
            row.createCell(1).setCellValue(user.getEmployeeId());
            row.createCell(2).setCellValue(user.getNickname());
            row.createCell(3).setCellValue(user.getEmail());
            row.createCell(4).setCellValue(user.getPhone());
            row.createCell(5).setCellValue(user.getRole());
            row.createCell(6).setCellValue(user.getDepartment());
            row.createCell(7).setCellValue(user.getStatus() == 1 ? "启用" : "禁用");
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("用户信息.xlsx", "UTF-8"));

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public List<User> importUsers(MultipartFile file) throws IOException {
        List<User> users = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            User user = new User();
            user.setUsername(getCellStringValue(row.getCell(0)));
            user.setEmployeeId(getCellStringValue(row.getCell(1)));
            user.setNickname(getCellStringValue(row.getCell(2)));
            user.setEmail(getCellStringValue(row.getCell(3)));
            user.setPhone(getCellStringValue(row.getCell(4)));
            user.setRole(getCellStringValue(row.getCell(5)));
            user.setDepartment(getCellStringValue(row.getCell(6)));

            String status = getCellStringValue(row.getCell(7));
            user.setStatus("启用".equals(status) ? 1 : 0);

            users.add(user);
        }

        workbook.close();
        return users;
    }

    @Override
    public void exportExams(List<Exam> exams, HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("考试信息");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"课程名称", "考试类型", "考试日期", "开始时间", "结束时间", "考试地点", "考试说明"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        for (int i = 0; i < exams.size(); i++) {
            Row row = sheet.createRow(i + 1);
            Exam exam = exams.get(i);

            row.createCell(0).setCellValue(exam.getCourseName() != null ? exam.getCourseName() : ""); // 课程名称
            row.createCell(1).setCellValue(exam.getExamType());
            row.createCell(2).setCellValue(exam.getExamDate().toString());
            row.createCell(3).setCellValue(exam.getStartTime().toString());
            row.createCell(4).setCellValue(exam.getEndTime().toString());
            row.createCell(5).setCellValue(exam.getClassroomName() != null ? exam.getClassroomName() : ""); // 考试地点
            row.createCell(6).setCellValue(exam.getDescription() != null ? exam.getDescription() : ""); // 考试说明
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("考试信息.xlsx", "UTF-8"));

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public List<Exam> importExams(MultipartFile file) throws IOException {
        List<Exam> exams = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            Exam exam = new Exam();
            exam.setExamType(getCellStringValue(row.getCell(1)));

            String examDate = getCellStringValue(row.getCell(2));
            if (examDate != null) {
                exam.setExamDate(LocalDate.parse(examDate));
            }

            String startTime = getCellStringValue(row.getCell(3));
            if (startTime != null) {
                exam.setStartTime(LocalTime.parse(startTime));
            }

            String endTime = getCellStringValue(row.getCell(4));
            if (endTime != null) {
                exam.setEndTime(LocalTime.parse(endTime));
            }

            // 设置考试说明
            String description = getCellStringValue(row.getCell(6));
            if (description != null) {
                exam.setDescription(description);
            }

            exams.add(exam);
        }

        workbook.close();
        return exams;
    }

    @Override
    public void exportScheduleTable(Long userId, Long semesterId, HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("课程表");

        // 创建课程表格式
        String[] timeSlots = {"08:00-09:40", "10:00-11:40", "14:00-15:40", "16:00-17:40", "19:00-20:40"};
        String[] weekDays = {"时间", "周一", "周二", "周三", "周四", "周五", "周六", "周日"};

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < weekDays.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(weekDays[i]);
        }

        // 创建时间行
        for (int i = 0; i < timeSlots.length; i++) {
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(timeSlots[i]);

            // 这里需要根据实际的排课数据填充课程信息
            for (int j = 1; j < weekDays.length; j++) {
                row.createCell(j).setCellValue(""); // 课程信息
            }
        }

        // 自动调整列宽
        for (int i = 0; i < weekDays.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("课程表.xlsx", "UTF-8"));

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public void exportScores(List<ExamScore> scores, HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("成绩信息");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"学号", "学生姓名", "考试名称", "课程名称", "成绩", "等级", "状态", "备注", "录入时间"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        for (int i = 0; i < scores.size(); i++) {
            Row row = sheet.createRow(i + 1);
            ExamScore score = scores.get(i);

            row.createCell(0).setCellValue(score.getStudentNumber() != null ? score.getStudentNumber() : "");
            row.createCell(1).setCellValue(score.getStudentName() != null ? score.getStudentName() : "");
            row.createCell(2).setCellValue(score.getExamName() != null ? score.getExamName() : "");
            row.createCell(3).setCellValue(score.getCourseName() != null ? score.getCourseName() : "");
            row.createCell(4).setCellValue(score.getScore() != null ? score.getScore().toString() : "");
            row.createCell(5).setCellValue(score.getGrade() != null ? score.getGrade() : "");
            row.createCell(6).setCellValue(score.getStatus() != null ? score.getStatus() : "");
            row.createCell(7).setCellValue(score.getRemark() != null ? score.getRemark() : "");
            row.createCell(8).setCellValue(score.getGradeTime() != null ? score.getGradeTime().toString() : "");
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("成绩信息.xlsx", "UTF-8"));

        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public List<ExamScore> importScores(MultipartFile file) throws IOException {
        List<ExamScore> scores = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            ExamScore score = new ExamScore();

            // 学号
            String studentNumber = getCellStringValue(row.getCell(0));
            if (studentNumber != null) {
                score.setStudentNumber(studentNumber);
            }

            // 学生姓名
            String studentName = getCellStringValue(row.getCell(1));
            if (studentName != null) {
                score.setStudentName(studentName);
            }

            // 成绩
            String scoreValue = getCellStringValue(row.getCell(4));
            if (scoreValue != null) {
                try {
                    score.setScore(new java.math.BigDecimal(scoreValue));
                } catch (NumberFormatException e) {
                    // 忽略无效的成绩值
                }
            }

            // 等级
            String grade = getCellStringValue(row.getCell(5));
            if (grade != null) {
                score.setGrade(grade);
            }

            // 状态
            String status = getCellStringValue(row.getCell(6));
            if (status != null) {
                score.setStatus(status);
            } else {
                score.setStatus("PRESENT"); // 默认状态
            }

            // 备注
            String remark = getCellStringValue(row.getCell(7));
            if (remark != null) {
                score.setRemark(remark);
            }

            scores.add(score);
        }

        workbook.close();
        return scores;
    }

    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }
}
