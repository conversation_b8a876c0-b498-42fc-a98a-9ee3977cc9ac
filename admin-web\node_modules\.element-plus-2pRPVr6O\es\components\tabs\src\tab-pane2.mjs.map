{"version": 3, "file": "tab-pane2.mjs", "sources": ["../../../../../../packages/components/tabs/src/tab-pane.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"shouldBeRender\"\n    v-show=\"active\"\n    :id=\"`pane-${paneName}`\"\n    ref=\"paneRef\"\n    :class=\"ns.b()\"\n    role=\"tabpanel\"\n    :aria-hidden=\"!active\"\n    :aria-labelledby=\"`tab-${paneName}`\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onBeforeUnmount,\n  onBeforeUpdate,\n  reactive,\n  ref,\n  useSlots,\n  watch,\n} from 'vue'\nimport { eagerComputed } from '@vueuse/core'\nimport { throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport { tabPaneProps } from './tab-pane'\n\nconst COMPONENT_NAME = 'ElTabPane'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(tabPaneProps)\n\nconst instance = getCurrentInstance()!\nconst slots = useSlots()\n\nconst tabsRoot = inject(tabsRootContextKey)\nif (!tabsRoot)\n  throwError(COMPONENT_NAME, 'usage: <el-tabs><el-tab-pane /></el-tabs/>')\n\nconst ns = useNamespace('tab-pane')\n\nconst paneRef = ref<HTMLDivElement>()\nconst index = ref<string>()\nconst isClosable = computed(() => props.closable || tabsRoot.props.closable)\nconst active = eagerComputed(\n  () => tabsRoot.currentName.value === (props.name ?? index.value)\n)\nconst loaded = ref(active.value)\nconst paneName = computed(() => props.name ?? index.value)\nconst shouldBeRender = eagerComputed(\n  () => !props.lazy || loaded.value || active.value\n)\n\nconst isFocusInsidePane = () => {\n  return paneRef.value?.contains(document.activeElement)\n}\n\nwatch(active, (val) => {\n  if (val) loaded.value = true\n})\n\nconst pane = reactive({\n  uid: instance.uid,\n  getVnode: () => instance.vnode,\n  slots,\n  props,\n  paneName,\n  active,\n  index,\n  isClosable,\n  isFocusInsidePane,\n})\n\ntabsRoot.registerPane(pane)\n\nonBeforeUnmount(() => {\n  tabsRoot.unregisterPane(pane)\n})\n\nonBeforeUpdate(() => {\n  if (slots.label) tabsRoot.nav$.value?.scheduleRender()\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;mCAkCc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAGA,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,QAAA,GAAW,OAAO,kBAAkB,CAAA,CAAA;AAC1C,IAAA,IAAI,CAAC,QAAA;AACH,MAAA,UAAA,CAAW,gBAAgB,4CAA4C,CAAA,CAAA;AAEzE,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAElC,IAAA,MAAM,UAAU,GAAoB,EAAA,CAAA;AACpC,IAAA,MAAM,QAAQ,GAAY,EAAA,CAAA;AAC1B,IAAA,MAAM,aAAa,QAAS,CAAA,MAAM,MAAM,QAAY,IAAA,QAAA,CAAS,MAAM,QAAQ,CAAA,CAAA;AAC3E,IAAA,MAAM,MAAS,GAAA,aAAA,CAAA,MAAA;AAAA,MACb,MAAM,CAAS;AAA2C,MAC5D,OAAA,QAAA,CAAA,WAAA,CAAA,KAAA,MAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACA,KAAM,CAAA,CAAA;AACN,IAAA,MAAM,YAAoB,CAAA,MAAA,CAAA,KAAA,CAAM,CAAM;AACtC,IAAA,MAAM,QAAiB,GAAA,QAAA,CAAA,MAAA;AAAA,MACrB,MAAM,CAAC;AAAqC,MAC9C,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,cAAe,GAAA,aAAgB,CAAA,MAAsB,CAAA,KAAA,CAAA,IAAA,IAAA,MAAA,CAAA,KAAA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IACvD,MAAA,iBAAA,GAAA,MAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAI,OAAA,CAAK,YAAe,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AAAA,KACzB,CAAA;AAED,IAAA,KAAA,CAAM,QAAgB,CAAA,GAAA,KAAA;AAAA,MACpB,OAAc;AAAA,QACd,MAAA,CAAA,QAAyB,IAAA,CAAA;AAAA,KACzB,CAAA,CAAA;AAAA,IACA,MAAA,IAAA,GAAA,QAAA,CAAA;AAAA,MACA,GAAA,EAAA,QAAA,CAAA,GAAA;AAAA,MACA,QAAA,EAAA,MAAA,QAAA,CAAA,KAAA;AAAA,MACA,KAAA;AAAA,MACA,KAAA;AAAA,MACA,QAAA;AAAA,MACD,MAAA;AAED,MAAA,KAAA;AAEA,MAAA,UAAA;AACE,MAAA;AAA4B,KAC7B,CAAA,CAAA;AAED,IAAA,QAAA,CAAA,YAAqB,CAAA,IAAA,CAAA,CAAA;AACnB,IAAA,eAAU,CAAA,MAAgB;AAA2B,MACtD,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}