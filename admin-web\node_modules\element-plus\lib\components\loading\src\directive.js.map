{"version": 3, "file": "directive.js", "sources": ["../../../../../../packages/components/loading/src/directive.ts"], "sourcesContent": ["// @ts-nocheck\nimport { isRef, ref } from 'vue'\nimport { hyphenate, isObject, isString } from '@element-plus/utils'\nimport Loading from './service'\n\nimport type { Directive, DirectiveBinding, UnwrapRef } from 'vue'\nimport type { LoadingOptions } from './types'\nimport type { LoadingInstance } from './loading'\n\nconst INSTANCE_KEY = Symbol('ElLoading')\nconst getAttributeName = (name: string) => {\n  return `element-loading-${hyphenate(name)}`\n}\n\nexport type LoadingBinding = boolean | UnwrapRef<LoadingOptions>\nexport interface ElementLoading extends HTMLElement {\n  [INSTANCE_KEY]?: {\n    instance: LoadingInstance\n    options: LoadingOptions\n  }\n}\n\nconst createInstance = (\n  el: ElementLoading,\n  binding: DirectiveBinding<LoadingBinding>\n) => {\n  const vm = binding.instance\n\n  const getBindingProp = <K extends keyof LoadingOptions>(\n    key: K\n  ): LoadingOptions[K] =>\n    isObject(binding.value) ? binding.value[key] : undefined\n\n  const resolveExpression = (key: any) => {\n    const data = (isString(key) && vm?.[key]) || key\n    return ref(data)\n  }\n\n  const getProp = <K extends keyof LoadingOptions>(name: K) =>\n    resolveExpression(\n      getBindingProp(name) || el.getAttribute(getAttributeName(name))\n    )\n\n  const fullscreen =\n    getBindingProp('fullscreen') ?? binding.modifiers.fullscreen\n\n  const options: LoadingOptions = {\n    text: getProp('text'),\n    svg: getProp('svg'),\n    svgViewBox: getProp('svgViewBox'),\n    spinner: getProp('spinner'),\n    background: getProp('background'),\n    customClass: getProp('customClass'),\n    fullscreen,\n    target: getBindingProp('target') ?? (fullscreen ? undefined : el),\n    body: getBindingProp('body') ?? binding.modifiers.body,\n    lock: getBindingProp('lock') ?? binding.modifiers.lock,\n  }\n  const instance = Loading(options)\n  instance._context = vLoading._context\n  el[INSTANCE_KEY] = {\n    options,\n    instance,\n  }\n}\n\nconst updateOptions = (\n  originalOptions: LoadingOptions,\n  newOptions: UnwrapRef<LoadingOptions>\n) => {\n  for (const key of Object.keys(originalOptions)) {\n    if (isRef(originalOptions[key]))\n      originalOptions[key].value = newOptions[key]\n  }\n}\n\nconst vLoading: Directive<ElementLoading, LoadingBinding> = {\n  mounted(el, binding) {\n    if (binding.value) {\n      createInstance(el, binding)\n    }\n  },\n  updated(el, binding) {\n    const instance = el[INSTANCE_KEY]\n    if (!binding.value) {\n      instance?.instance.close()\n      el[INSTANCE_KEY] = null\n      return\n    }\n\n    if (!instance) createInstance(el, binding)\n    else {\n      updateOptions(\n        instance.options,\n        isObject(binding.value)\n          ? binding.value\n          : {\n              text: el.getAttribute(getAttributeName('text')),\n              svg: el.getAttribute(getAttributeName('svg')),\n              svgViewBox: el.getAttribute(getAttributeName('svgViewBox')),\n              spinner: el.getAttribute(getAttributeName('spinner')),\n              background: el.getAttribute(getAttributeName('background')),\n              customClass: el.getAttribute(getAttributeName('customClass')),\n            }\n      )\n    }\n  },\n  unmounted(el) {\n    el[INSTANCE_KEY]?.instance.close()\n    el[INSTANCE_KEY] = null\n  },\n}\n\nvLoading._context = null\nexport default vLoading\n"], "names": ["hyphenate", "isObject", "isString", "ref", "Loading", "isRef"], "mappings": ";;;;;;;;AAGA,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AACzC,MAAM,gBAAgB,GAAG,CAAC,IAAI,KAAK;AACnC,EAAE,OAAO,CAAC,gBAAgB,EAAEA,gBAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,CAAC,EAAE,EAAE,OAAO,KAAK;AACxC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrB,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAG,CAAC,GAAG,KAAKC,eAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AACxF,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACrC,IAAI,MAAM,IAAI,GAAGC,eAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;AACzE,IAAI,OAAOC,OAAG,CAAC,IAAI,CAAC,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,IAAI,KAAK,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/G,EAAE,MAAM,UAAU,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC;AACrG,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC;AACzB,IAAI,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC;AACvB,IAAI,UAAU,EAAE,OAAO,CAAC,YAAY,CAAC;AACrC,IAAI,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC;AAC/B,IAAI,UAAU,EAAE,OAAO,CAAC,YAAY,CAAC;AACrC,IAAI,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC;AACvC,IAAI,UAAU;AACd,IAAI,MAAM,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,UAAU,GAAG,KAAK,CAAC,GAAG,EAAE;AACnF,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI;AAC7E,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI;AAC7E,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAGC,kBAAO,CAAC,OAAO,CAAC,CAAC;AACpC,EAAE,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;AACxC,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG;AACrB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK;AACvD,EAAE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AAClD,IAAI,IAAIC,SAAK,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AACnC,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACnD,GAAG;AACH,CAAC,CAAC;AACG,MAAC,QAAQ,GAAG;AACjB,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AACvB,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AACvB,MAAM,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAClC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AACvB,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC;AACtC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AACxB,MAAM,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC5D,MAAM,EAAE,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;AAC9B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAClC,SAAS;AACT,MAAM,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAEJ,eAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG;AAChF,QAAQ,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AACvD,QAAQ,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACrD,QAAQ,UAAU,EAAE,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACnE,QAAQ,OAAO,EAAE,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AAC7D,QAAQ,UAAU,EAAE,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACnE,QAAQ,WAAW,EAAE,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AACrE,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;AACnE,IAAI,EAAE,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;AAC5B,GAAG;AACH,EAAE;AACF,QAAQ,CAAC,QAAQ,GAAG,IAAI;;;;"}