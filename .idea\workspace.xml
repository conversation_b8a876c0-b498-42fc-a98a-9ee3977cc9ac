<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1269ec3b-c9e3-4346-a58c-bbe15070b735" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\java\maven\apache-maven-3.6.0" />
        <option name="localRepository" value="E:\java\maven\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\java\maven\apache-maven-3.6.0\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="30JJEKBXvMldrCl7jyDu3dCdJ7V" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JavaScript 调试.test-api.html.executor": "Run",
    "JavaScript 调试.test_classroom_api.html.executor": "Run",
    "JavaScript 调试.test_excel_export.html.executor": "Debug",
    "Maven.school-course-schedule [clean].executor": "Run",
    "Maven.school-course-schedule [install].executor": "Run",
    "Maven.school_course_schedule [clean].executor": "Run",
    "Maven.school_course_schedule [install].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.SchoolCourseScheduleApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/school_course_schedule/src/main/java/com/school_course_schedule",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.16210938",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "E:\\java\\idea\\IntelliJ IDEA 2024.1.4\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true",
    "应用程序.PasswordGenerator (1).executor": "Run",
    "应用程序.PasswordGenerator.executor": "Run",
    "应用程序.SchoolCourseScheduleApplication.executor": "Run"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\school_course_schedule\src\main\java\com\school_course_schedule" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.school_course_schedule" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.SchoolCourseScheduleApplication">
    <configuration name="PasswordGenerator (1)" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="PasswordGenerator" />
      <module name="school_course_schedule" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PasswordGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.school_course_schedule.PasswordGenerator" />
      <module name="school_course_schedule" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="test_classroom_api.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/school_course_schedule/test_classroom_api.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="test_excel_export.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/school_course_schedule/test_excel_export.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="SchoolCourseScheduleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="school_course_schedule" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.school_course_schedule.SchoolCourseScheduleApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.school_course_schedule.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.SchoolCourseScheduleApplication" />
        <item itemvalue="JavaScript 调试.test_excel_export.html" />
        <item itemvalue="应用程序.PasswordGenerator (1)" />
        <item itemvalue="应用程序.PasswordGenerator" />
        <item itemvalue="JavaScript 调试.test_classroom_api.html" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="1269ec3b-c9e3-4346-a58c-bbe15070b735" name="更改" comment="" />
      <created>1753339631544</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753339631544</updated>
      <workItem from="1753339632749" duration="3298000" />
      <workItem from="1753343765866" duration="57649000" />
      <workItem from="1753461463459" duration="321000" />
      <workItem from="1753461988175" duration="496000" />
      <workItem from="1753504214890" duration="4977000" />
      <workItem from="1753521942095" duration="75077000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>