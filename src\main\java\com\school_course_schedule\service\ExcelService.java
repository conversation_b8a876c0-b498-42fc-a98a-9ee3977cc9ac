package com.school_course_schedule.service;

import com.school_course_schedule.entity.Course;
import com.school_course_schedule.entity.Exam;
import com.school_course_schedule.entity.ExamScore;
import com.school_course_schedule.entity.Schedule;
import com.school_course_schedule.entity.User;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Excel导入导出服务接口
 */
public interface ExcelService {
    
    /**
     * 导出课程信息到Excel
     */
    void exportCourses(List<Course> courses, HttpServletResponse response) throws IOException;
    
    /**
     * 从Excel导入课程信息
     */
    List<Course> importCourses(MultipartFile file) throws IOException;
    
    /**
     * 导出排课信息到Excel
     */
    void exportSchedules(List<Schedule> schedules, HttpServletResponse response) throws IOException;
    
    /**
     * 从Excel导入排课信息
     */
    List<Schedule> importSchedules(MultipartFile file) throws IOException;
    
    /**
     * 导出用户信息到Excel
     */
    void exportUsers(List<User> users, HttpServletResponse response) throws IOException;
    
    /**
     * 从Excel导入用户信息
     */
    List<User> importUsers(MultipartFile file) throws IOException;
    
    /**
     * 导出考试信息到Excel
     */
    void exportExams(List<Exam> exams, HttpServletResponse response) throws IOException;
    
    /**
     * 从Excel导入考试信息
     */
    List<Exam> importExams(MultipartFile file) throws IOException;
    
    /**
     * 导出课程表到Excel
     */
    void exportScheduleTable(Long userId, Long semesterId, HttpServletResponse response) throws IOException;

    /**
     * 导出成绩信息到Excel
     */
    void exportScores(List<ExamScore> scores, HttpServletResponse response) throws IOException;

    /**
     * 从Excel导入成绩信息
     */
    List<ExamScore> importScores(MultipartFile file) throws IOException;
} 