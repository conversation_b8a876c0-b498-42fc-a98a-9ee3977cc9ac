<template>
  <div class="schedules-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>智能排课系统</h2>
        <p>拖拽式排课界面，支持实时冲突检测和自动排课</p>
      </div>
      <div class="header-actions">
        <el-button @click="autoSchedule" type="primary" :loading="autoScheduling">
          <el-icon><Aim /></el-icon>
          自动排课
        </el-button>
        <el-button @click="clearSchedule" type="warning">
          <el-icon><Delete /></el-icon>
          清空排课
        </el-button>
        <el-button @click="exportSchedule">
          <el-icon><Download /></el-icon>
          导出课表
        </el-button>
        <el-button @click="saveSchedule" type="success" :loading="saving">
          <el-icon><Check /></el-icon>
          保存排课
        </el-button>
      </div>
    </div>

    <!-- 控制面板 -->
    <el-row :gutter="20">
      <!-- 左侧：待排课程和统计 -->
      <el-col :xs="24" :lg="6">
        <!-- 学期选择 - 精简版 -->
        <el-card class="control-card compact-card">
          <template #header>
            <h3>学期设置</h3>
          </template>
          <el-form label-position="top" size="small">
            <el-form-item label="学期">
              <el-select v-model="currentSemester" placeholder="选择学期" style="width: 100%;" @change="handleSemesterChange">
                <el-option label="2025春季学期" value="2025-spring" />
                <el-option label="2025秋季学期" value="2025-autumn" />
              </el-select>
            </el-form-item>
            <el-form-item label="周次">
              <el-input-number v-model="currentWeek" :min="1" :max="20" style="width: 100%;" />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 待排课程 - 紧凑版 -->
        <el-card class="control-card course-list-card compact-card">
          <template #header>
            <div class="card-header">
              <h3>待排课程</h3>
              <el-badge :value="unscheduledCourses.length" type="warning" />
            </div>
          </template>
          <!-- 搜索框 -->
          <div class="course-search">
            <el-input
              v-model="courseSearchKeyword"
              placeholder="搜索课程..."
              size="small"
              clearable
              @input="filterCourses"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 课程列表 - 限制高度并添加滚动 -->
          <div class="course-list" ref="courseListRef">
            <div v-if="filteredCourses.length === 0 && unscheduledCourses.length > 0" class="empty-search">
              <el-icon><DocumentRemove /></el-icon>
              <p>未找到匹配的课程</p>
            </div>
            <div v-else-if="unscheduledCourses.length === 0" class="empty-courses">
              <el-icon><Check /></el-icon>
              <p>所有课程已安排</p>
            </div>
            <div
              v-for="course in filteredCourses"
              :key="course.id"
              class="course-item"
              :class="{ 'dragging': draggingCourse?.id === course.id }"
              draggable="true"
              @dragstart="handleDragStart($event, course)"
              @dragend="handleDragEnd"
            >
              <div class="course-header">
                <h4>{{ course.name }}</h4>
                <el-tag size="small" :type="getCourseTypeColor(course.type)">
                  {{ course.credits }}学分
                </el-tag>
              </div>
              <div class="course-info">
                <div class="info-item">
                  <el-icon><User /></el-icon>
                  <span>{{ course.teacherName }}</span>
                </div>
                <div class="info-item">
                  <el-icon><Clock /></el-icon>
                  <span>{{ course.duration }}节</span>
                </div>
              </div>
              <div class="course-requirements" v-if="course.requirements">
                <el-icon><InfoFilled /></el-icon>
                <span>{{ course.requirements }}</span>
              </div>
            </div>
          </div>

          <!-- 显示更多按钮 -->
<!--          <div v-if="unscheduledCourses.length > 6" class="course-list-footer">-->
<!--            <el-button size="small" text @click="expandCourseList = !expandCourseList">-->
<!--              <span v-if="!expandCourseList">显示更多 ({{ unscheduledCourses.length - 6 }})</span>-->
<!--              <span v-else>收起</span>-->
<!--              <el-icon><ArrowDown v-if="!expandCourseList" /><ArrowUp v-else /></el-icon>-->
<!--            </el-button>-->
<!--          </div>-->
        </el-card>

        <!-- 排课统计 - 使用新组件 -->
        <ScheduleStats
          :semester-id="1"
          @refresh="handleStatsRefresh"
          @quick-action="handleQuickAction"
          ref="scheduleStatsRef"
        />

        <!-- 智能自动排课 -->
        <AutoSchedule
          :unscheduled-courses="unscheduledCourses"
          :available-classrooms="availableClassrooms.length"
          :semester-id="1"
          @schedule-complete="handleScheduleComplete"
          @refresh-data="handleDataRefresh"
          ref="autoScheduleRef"
        />

        <!-- 详细统计信息 -->
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <h3>详细信息</h3>
            </div>
          </template>
          <div class="detailed-stats">
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">未排课程</span>
                <span class="detail-value">{{ unscheduledCourses.length }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">排课记录</span>
                <span class="detail-value">{{ scheduledCourses.length }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">当前学期</span>
                <span class="detail-value">{{ currentSemester }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">当前周次</span>
                <span class="detail-value">第{{ currentWeek }}周</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：课表网格 -->
      <el-col :xs="24" :lg="18">
        <el-card class="schedule-card">
          <template #header>
            <div class="schedule-header">
              <div class="header-left">
                <h3>课程表 - 第{{ currentWeek }}周</h3>
                <el-tag v-if="scheduledCourses.length > 0" type="success" size="small">
                  已排{{ scheduledCourses.length }}门课程
                </el-tag>
                <el-tag v-else type="info" size="small">
                  暂无排课
                </el-tag>
              </div>
              <div class="schedule-controls">
                <el-button size="small" @click="refreshAllData" :loading="loading" type="primary">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
                <el-switch
                  v-model="showConflicts"
                  active-text="显示冲突"
                  inactive-text="隐藏冲突"
                />
                <el-switch
                  v-model="showClassrooms"
                  active-text="显示教室"
                  inactive-text="隐藏教室"
                />
              </div>
            </div>
          </template>

          <!-- 课表网格 -->
          <div class="schedule-grid">
            <!-- 调试信息 -->
            <div v-if="!weekDays || weekDays.length === 0" style="padding: 20px; color: red;">
              调试: weekDays未定义或为空 - {{ weekDays }}
            </div>
            <div v-if="!timePeriods || timePeriods.length === 0" style="padding: 20px; color: red;">
              调试: timePeriods未定义或为空 - {{ timePeriods }}
            </div>

            <!-- 表头 -->
            <div class="grid-header" v-if="weekDays && weekDays.length > 0">
              <div class="time-header">时间/星期</div>
              <div
                v-for="day in weekDays"
                :key="day.value"
                class="day-header"
              >
                <div class="day-name">{{ day.label }}</div>
                <div class="day-date">{{ getDateString(day.value) }}</div>
              </div>
            </div>

            <!-- 表体 -->
            <div class="grid-body" v-if="timePeriods && timePeriods.length > 0 && weekDays && weekDays.length > 0">
              <div
                v-for="(period, periodIndex) in timePeriods"
                :key="period.value"
                class="grid-row"
              >
                <!-- 时间列 -->
                <div class="time-cell">
                  <div class="time-slot">{{ period.label }}</div>
                  <div class="time-range">{{ period.time }}</div>
                </div>

                <!-- 课程单元格 -->
                <div
                  v-for="(day, dayIndex) in weekDays"
                  :key="`${day.value}-${period.value}`"
                  class="schedule-cell"
                  :class="getCellClass(day.value, period.value)"
                  @drop="handleDrop($event, day.value, period.value)"
                  @dragover="handleDragOver($event, day.value, period.value)"
                  @dragleave="handleDragLeave"
                  @click="handleCellClick(day.value, period.value)"
                >
                  <!-- 课程卡片 -->
                  <div
                    v-if="getCellCourse(day.value, period.value)"
                    class="course-card"
                    :class="getCourseCardClass(getCellCourse(day.value, period.value))"
                    draggable="true"
                    @dragstart="handleScheduledCourseDragStart($event, getCellCourse(day.value, period.value), day.value, period.value)"
                    @dragend="handleDragEnd"
                    @contextmenu.prevent="showCourseMenu($event, getCellCourse(day.value, period.value))"
                  >
                    <div class="course-name">{{ getCellCourse(day.value, period.value)?.courseName || getCellCourse(day.value, period.value)?.name || 'Unknown Course' }}</div>
                    <div class="course-teacher">{{ getCellCourse(day.value, period.value)?.teacherName || 'Unknown Teacher' }}</div>
                    <div class="course-classroom" v-if="showClassrooms">
                      {{ getCellCourse(day.value, period.value)?.classroomName || '' }}
                    </div>
                    <div class="course-note" v-if="getCellCourse(day.value, period.value)?.note">
                      {{ getCellCourse(day.value, period.value).note }}
                    </div>
                    <div class="course-actions">
                      <el-button
                        size="small"
                        circle
                        @click.stop="editScheduledCourse(getCellCourse(day.value, period.value))"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button
                        size="small"
                        circle
                        type="danger"
                        @click.stop="removeScheduledCourse(day.value, period.value)"
                      >
                        <el-icon><Close /></el-icon>
                      </el-button>
                    </div>
                  </div>

                  <!-- 冲突提示 -->
                  <div
                    v-if="showConflicts && getCellConflicts(day.value, period.value).length > 0"
                    class="conflict-indicator"
                    @click="showConflictDetails(day.value, period.value)"
                  >
                    <el-icon><WarningFilled /></el-icon>
                    <span>{{ getCellConflicts(day.value, period.value).length }}个冲突</span>
                  </div>

                  <!-- 拖拽提示 -->
                  <div
                    v-if="isDragOver && dragOverCell.day === day.value && dragOverCell.period === period.value"
                    class="drop-indicator"
                    :class="{ 'can-drop': canDropHere(day.value, period.value), 'cannot-drop': !canDropHere(day.value, period.value) }"
                  >
                    <el-icon v-if="canDropHere(day.value, period.value)"><Plus /></el-icon>
                    <el-icon v-else><Close /></el-icon>
                    <span>{{ canDropHere(day.value, period.value) ? '可以放置' : '不能放置' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 冲突详情对话框 -->
    <el-dialog v-model="conflictDialogVisible" title="冲突详情" width="600px">
      <div class="conflict-details">
        <el-alert
          title="检测到以下冲突，请及时处理"
          type="warning"
          show-icon
          :closable="false"
        />
        <div class="conflict-list">
          <div
            v-for="(conflict, index) in currentConflicts"
            :key="index"
            class="conflict-item"
          >
            <div class="conflict-type">
              <el-tag :type="conflict.type === 'time' ? 'danger' : 'warning'">
                {{ conflict.type === 'time' ? '时间冲突' : '教室冲突' }}
              </el-tag>
            </div>
            <div class="conflict-description">{{ conflict.description }}</div>
            <div class="conflict-courses">
              <div v-for="course in conflict.courses" :key="course.id" class="conflict-course">
                {{ course.name }} - {{ course.teacherName }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="conflictDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="resolveConflicts">自动解决冲突</el-button>
      </template>
    </el-dialog>

    <!-- 课程编辑对话框 -->
    <el-dialog v-model="courseEditDialogVisible" :title="editingCourse ? '编辑课程安排' : '新增课程安排'" width="500px">
      <el-form :model="courseEditForm" label-width="100px">
        <el-form-item label="课程">
          <el-select v-model="courseEditForm.courseId" placeholder="选择课程">
            <el-option
              v-for="course in allCourses"
              :key="course.id"
              :label="course.name"
              :value="course.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="教室">
          <el-select v-model="courseEditForm.classroomId" placeholder="选择教室">
            <el-option
              v-for="classroom in availableClassrooms"
              :key="classroom.id"
              :label="`${classroom.building}-${classroom.roomNumber}`"
              :value="classroom.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="星期">
          <el-select v-model="courseEditForm.dayOfWeek" placeholder="选择星期">
            <el-option
              v-for="day in weekDays"
              :key="day.value"
              :label="day.label"
              :value="day.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="节次">
          <el-select v-model="courseEditForm.period" placeholder="选择节次">
            <el-option
              v-for="period in timePeriods"
              :key="period.value"
              :label="period.label"
              :value="period.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="courseEditForm.remarks" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="courseEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCurrentSchedule" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 排课状态监控组件 -->
    <ScheduleMonitor
      :scheduled-courses="scheduledCourses"
      :unscheduled-courses="unscheduledCourses"
      :all-courses="allCourses"
      :week-days="weekDays"
      :time-periods="timePeriods"
      ref="scheduleMonitorRef"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Calendar,
  Plus,
  Delete,
  Edit,
  Check,
  Download,
  Aim,
  Refresh,
  TrendCharts,
  User,
  DataAnalysis,
  Collection,
  WarningFilled,
  Clock,
  Search,
  DocumentRemove,
  ArrowDown,
  ArrowUp
} from '@element-plus/icons-vue'

// 导入新组件
import ScheduleStats from '../components/ScheduleStats.vue'
import AutoSchedule from '../components/AutoSchedule.vue'
import ScheduleMonitor from '../components/ScheduleMonitor.vue'
import { apiRequest } from '../utils/api'

// 响应式数据
const autoScheduling = ref(false)
const saving = ref(false)
const draggingCourse = ref(null)
const isDragOver = ref(false)
const dragOverCell = reactive({ day: null, period: null })
const showConflicts = ref(true)
const showClassrooms = ref(true)
const conflictDialogVisible = ref(false)
const courseEditDialogVisible = ref(false)
const editingCourse = ref(null)
const expandCourseList = ref(false) // 新增：控制课程列表是否展开
const courseSearchKeyword = ref('') // 新增：课程搜索关键词
const filteredCourses = ref([]) // 新增：过滤后的课程列表

// 组件引用
const scheduleStatsRef = ref(null)
const autoScheduleRef = ref(null)
const courseListRef = ref(null) // 新增：课程列表的DOM引用
const scheduleMonitorRef = ref(null) // 新增：监控组件引用

// 处理统计刷新
const handleStatsRefresh = async () => {
  await loadScheduleData()
  console.log('统计数据已刷新')
}

// 处理快速操作
const handleQuickAction = async (action) => {
  console.log('处理快速操作:', action)

  switch (action) {
    case 'auto-schedule':
      // 触发自动排课
      if (autoScheduleRef.value) {
        await autoScheduleRef.value.startAutoSchedule()
      } else {
        await autoSchedule()
      }
      break
    case 'view-schedule':
      // 滚动到课表区域
      const scheduleGrid = document.querySelector('.schedule-grid')
      if (scheduleGrid) {
        scheduleGrid.scrollIntoView({ behavior: 'smooth' })
      }
      break
    case 'export':
      // 导出课表
      await exportSchedule()
      break
    case 'refresh':
      // 刷新数据
      await refreshAllData()
      break
    default:
      console.log('未知的快速操作:', action)
  }
}

// 处理自动排课完成
const handleScheduleComplete = async () => {
  console.log('自动排课完成，开始刷新数据...')

  try {
    // 重新加载所有数据
    await loadScheduleData()

    // 刷新统计数据
    if (scheduleStatsRef.value) {
      await scheduleStatsRef.value.refreshStats()
    }

    // 显示成功提示
    ElMessage.success({
      message: '自动排课完成，数据已更新！',
      duration: 3000,
      showClose: true
    })

    console.log('数据刷新完成')
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage.error('数据刷新失败，请手动刷新页面')
  }
}

// 处理数据刷新 (从AutoSchedule组件触发)
const handleDataRefresh = async () => {
  console.log('处理数据刷新请求...')

  try {
    // 重新加载所有数据
    await loadScheduleData()

    // 同时刷新统计数据
    if (scheduleStatsRef.value) {
      await scheduleStatsRef.value.loadStats()
    }

    console.log('数据刷新完成，当前排课数量:', scheduledCourses.value.length)
    console.log('当前未排课程数量:', unscheduledCourses.value.length)

    // 强制重新渲染
    await nextTick()

  } catch (error) {
    console.error('数据刷新失败:', error)
  }
}

  // 刷新所有数据
  const refreshAllData = async () => {
    console.log('开始刷新所有数据...')

    try {
      // 显示加载提示
      const loading = ElMessage.info({
        message: '正在刷新数据...',
        duration: 0 // 不自动关闭
      })

      // 并行刷新所有数据
      const promises = [
        loadScheduleData(),
        scheduleStatsRef.value?.loadStats(),
      ]

      await Promise.all(promises.filter(Boolean))

      // 关闭加载提示
      loading.close()

      ElMessage.success({
        message: '数据已刷新',
        duration: 2000
      })

      console.log('所有数据刷新完成')
    } catch (error) {
      console.error('数据刷新失败:', error)
      ElMessage.error('数据刷新失败，请稍后重试')
    }
  }

// 基础数据
const currentSemester = ref('2025-autumn')
const currentWeek = ref(1)
const unscheduledCourses = ref([])
const scheduledCourses = ref([])
const allCourses = ref([])
const availableClassrooms = ref([])
const currentConflicts = ref([])

// 时间定义
const weekDays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 时间段定义 - 修正为与数据库一致的ID
const timePeriods = [
  { label: '第1-2节', value: 1, time: '08:00-09:40' },
  { label: '第3-4节', value: 2, time: '10:00-11:40' },
  { label: '第5-6节', value: 3, time: '14:00-15:40' },
  { label: '第7-8节', value: 4, time: '16:00-17:40' },
  { label: '第9-10节', value: 5, time: '19:00-20:40' }
]

// 课程编辑表单
const courseEditForm = reactive({
  courseId: null,
  classroomId: null,
  dayOfWeek: null,
  period: null,
  remarks: ''
})

// 排课统计
const refreshingStats = ref(false)

const scheduleStats = computed(() => {
  const total = allCourses.value.length
  // 统计已排课程的唯一ID数量，而不是排课记录数量
  const uniqueScheduledCourseIds = new Set(scheduledCourses.value.map(sc => sc.courseId))
  const scheduled = uniqueScheduledCourseIds.size
  const conflicts = getAllConflicts().length
  const completionRate = total > 0 ? Math.round((scheduled / total) * 100) : 0

  console.log('统计数据:', {
    总课程: total,
    已排课程: scheduled,
    排课记录: scheduledCourses.value.length,
    冲突数: conflicts,
    完成率: completionRate
  })

  return {
    total,
    scheduled,
    conflicts,
    completionRate
  }
})

// 平均学分计算
const averageCredits = computed(() => {
  if (allCourses.value.length === 0) return '0.0'
  const totalCredits = allCourses.value.reduce((sum, course) => {
    return sum + (course.credits || 0)
  }, 0)
  return (totalCredits / allCourses.value.length).toFixed(1)
})

// 总学时计算
const totalHours = computed(() => {
  return allCourses.value.reduce((sum, course) => {
    // 假设每学分对应16学时
    return sum + ((course.credits || 0) * 16)
  }, 0)
})

// 刷新统计数据
const refreshStats = async () => {
  refreshingStats.value = true
  try {
    await loadScheduleData()
    ElMessage.success('统计数据已刷新')
  } catch (error) {
    console.error('刷新统计失败:', error)
    ElMessage.error('刷新统计失败')
  } finally {
    refreshingStats.value = false
  }
}

// 获取日期字符串
const getDateString = (dayValue) => {
  // 简化实现，实际应该根据当前周计算具体日期
  const dayIndex = weekDays.findIndex(d => d.value === dayValue)
  return `10/${15 + dayIndex}`
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  if (percentage >= 40) return '#f56c6c'
  return '#909399'
}

// 拖拽相关方法
const handleDragStart = (event, course) => {
  console.log('拖拽开始 - 未排课程:', course)
  draggingCourse.value = course
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/plain', JSON.stringify(course))
}

const handleDragEnd = () => {
  draggingCourse.value = null
  isDragOver.value = false
  dragOverCell.day = null
  dragOverCell.period = null
}

const handleDragOver = (event, day, period) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'move'
  isDragOver.value = true
  dragOverCell.day = day
  dragOverCell.period = period
}

const handleDragLeave = () => {
  isDragOver.value = false
  dragOverCell.day = null
  dragOverCell.period = null
}

const handleDrop = (event, day, period) => {
  event.preventDefault()
  isDragOver.value = false

  try {
    const courseData = JSON.parse(event.dataTransfer.getData('text/plain'))
    console.log('拖拽放下 - 解析的课程数据:', courseData)

    if (canDropHere(day, period)) {
      scheduleCourseToPeriod(courseData, day, period)
    } else {
      ElMessage.warning('无法在此时间段安排课程，存在冲突')
    }
  } catch (error) {
    console.error('处理拖拽失败:', error)
  }
}

// 冲突检测
const canDropHere = (day, period) => {
  if (!draggingCourse.value) return false

  // 检查时间冲突
  const existingCourse = getCellCourse(day, period)
  if (existingCourse) return false

  // 检查教师冲突
  const teacherConflict = scheduledCourses.value.some(sc =>
    sc.teacherId === draggingCourse.value.teacherId &&
    sc.dayOfWeek === day &&
    sc.period === period
  )

  return !teacherConflict
}

const getCellCourse = (day, period) => {
  const course = scheduledCourses.value.find(sc => {
    // 支持多种字段名格式
    const courseDay = sc.day || sc.dayOfWeek || sc.day_of_week
    const coursePeriod = sc.period || sc.timePeriodId || sc.time_period_id

    // 确保数据类型一致
    const dayMatch = String(courseDay) === String(day)
    const periodMatch = String(coursePeriod) === String(period)

    const matches = dayMatch && periodMatch
    if (matches) {
      console.log(`✅ 找到课程在 星期${day} 第${period}节:`, sc)
    }
    return matches
  })

  // 增强调试信息
  if (!course && scheduledCourses.value.length > 0) {
    console.log(`❌ 星期${day}第${period}节 没有课程`)
    console.log('当前所有已排课程的位置:')
    scheduledCourses.value.forEach((sc, index) => {
      const courseDay = sc.day || sc.dayOfWeek || sc.day_of_week
      const coursePeriod = sc.period || sc.timePeriodId || sc.time_period_id
      console.log(`  ${index + 1}. ${sc.courseName || sc.name} -> 星期${courseDay}第${coursePeriod}节`)
    })
    console.log('查询条件:', { day, period, dayType: typeof day, periodType: typeof period })
  }

  return course
}

const getCellConflicts = (day, period) => {
  const conflicts = []
  const course = getCellCourse(day, period)
  if (!course) return conflicts

  // 检查教师冲突
  const teacherConflicts = scheduledCourses.value.filter(sc =>
    sc.id !== course.id &&
    sc.teacherId === course.teacherId &&
    sc.dayOfWeek === day &&
    sc.period === period
  )

  if (teacherConflicts.length > 0) {
    conflicts.push({
      type: 'teacher',
      description: `教师${course.teacherName}时间冲突`,
      courses: [course, ...teacherConflicts]
    })
  }

  return conflicts
}

const getAllConflicts = () => {
  const conflicts = []
  weekDays.forEach(day => {
    timePeriods.forEach(period => {
      const cellConflicts = getCellConflicts(day.value, period.value)
      conflicts.push(...cellConflicts)
    })
  })
  return conflicts
}

// 样式相关方法
const getCellClass = (day, period) => {
  const classes = []
  const course = getCellCourse(day, period)

  if (course) classes.push('has-course')
  if (showConflicts.value && getCellConflicts(day, period).length > 0) classes.push('has-conflict')
  if (isDragOver.value && dragOverCell.day === day && dragOverCell.period === period) {
    classes.push(canDropHere(day, period) ? 'can-drop' : 'cannot-drop')
  }

  return classes
}

const getCourseCardClass = (course) => {
  const classes = ['scheduled']
  if (showConflicts.value && hasConflict(course)) classes.push('conflict')
  return classes
}

const getCourseTypeColor = (type) => {
  const colorMap = {
    '必修': 'danger',
    '选修': 'primary',
    '实验': 'warning',
    '实习': 'success'
  }
  return colorMap[type] || 'info'
}

const hasConflict = (course) => {
  return getAllConflicts().some(conflict =>
    conflict.courses.some(c => c.id === course.id)
  )
}

// 业务逻辑方法
// 已排课程拖拽处理
const handleScheduledCourseDragStart = (event, course, fromDay, fromPeriod) => {
  console.log('拖拽开始 - 已排课程:', course)

  draggingCourse.value = {
    ...course,
    fromDay,
    fromPeriod,
    isRescheduling: true
  }

  console.log('设置拖拽数据:', draggingCourse.value)

  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/plain', JSON.stringify(draggingCourse.value))
}

const scheduleCourseToPeriod = async (course, day, period) => {
  try {
    console.log('拖拽的课程数据:', course)

    // 如果是重新安排已有课程
    if (course.isRescheduling) {
      // 删除原位置的课程
      removeScheduledCourse(course.fromDay, course.fromPeriod)
    } else {
      // 从未排课程列表中移除
      const courseIndex = unscheduledCourses.value.findIndex(c => c.id === course.id)
      if (courseIndex !== -1) {
        unscheduledCourses.value.splice(courseIndex, 1)
      }
    }

    // 获取正确的课程信息
    let courseName, teacherName, courseId

    if (course.isRescheduling) {
      // 已排课程重新安排
      courseId = course.courseId || course.id
      courseName = course.courseName || course.name
      teacherName = course.teacherName
    } else {
      // 未排课程新安排
      courseId = course.id
      courseName = course.name
      teacherName = course.teacherName
    }

    console.log('解析后的课程信息:', { courseId, courseName, teacherName })

    // 添加到新位置
    const scheduledCourse = {
      id: Date.now(), // 临时ID，保存后会被后端真实ID替换
      courseId: courseId,
      courseName: courseName,
      teacherName: teacherName,
      classroomName: '',
      day: day,
      period: period,
      color: course.color || '#4CAF50'
    }

    scheduledCourses.value.push(scheduledCourse)
    console.log('添加到课程表的数据:', scheduledCourse)

    // 设置编辑表单数据并打开对话框
    Object.assign(courseEditForm, {
      courseId: courseId,
      classroomId: null,
      dayOfWeek: day,
      period: period,
      remarks: ''
    })

    courseEditDialogVisible.value = true

  } catch (error) {
    ElMessage.error('安排课程失败')
    console.error(error)
  }
}

const removeScheduledCourse = (day, period) => {
  const course = getCellCourse(day, period)
  if (course) {
    // 从已排课程中移除
    const index = scheduledCourses.value.findIndex(sc =>
      sc.day === day && sc.period === period
    )
    if (index > -1) {
      scheduledCourses.value.splice(index, 1)
    }

    // 找到对应的课程信息
    const courseInfo = allCourses.value.find(c => c.id === course.courseId)
    if (courseInfo) {
      // 添加回待排列表
      unscheduledCourses.value.push(courseInfo)
    }

    ElMessage.success('课程已移除')
  }
}

const getWeekDayLabel = (day) => {
  return weekDays.find(d => d.value === day)?.label || day
}

const getPeriodLabel = (period) => {
  return timePeriods.find(p => p.value === period)?.label || period
}

// 自动排课算法 (备用方法，主要使用AutoSchedule组件)
const autoSchedule = async () => {
  autoScheduling.value = true
  try {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    ElMessage.info({
      message: '正在自动排课...',
      duration: 2000
    })

    // 获取当前学期ID（使用默认值1）
    const semesterId = 1

    const response = await fetch(`http://localhost:8080/api/schedule/auto-schedule?semesterId=${semesterId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    const result = await response.json()

    if (response.ok && result.code === 200) {
      // 使用统一的完成处理方法
      await handleScheduleComplete()
    } else {
      ElMessage.error({
        message: result.message || '自动排课失败',
        duration: 5000,
        showClose: true
      })
    }
  } catch (error) {
    console.error('自动排课失败:', error)
    ElMessage.error({
      message: '自动排课失败，请检查网络连接',
      duration: 5000,
      showClose: true
    })
  } finally {
    autoScheduling.value = false
  }
}

const canScheduleCourse = (course, day, period) => {
  // 检查是否已有课程
  if (getCellCourse(day, period)) return false

  // 检查教师冲突
  const teacherConflict = scheduledCourses.value.some(sc =>
    sc.teacherId === course.teacherId &&
    sc.dayOfWeek === day &&
    sc.period === period
  )

  return !teacherConflict
}

// 数据加载
const loadScheduleData = async () => {
  try {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    console.log('开始加载课程表数据...')

    // 清空现有数据
    scheduledCourses.value = []
    availableClassrooms.value = []
    allCourses.value = []
    unscheduledCourses.value = []

    // 获取当前学期ID（使用默认值1）
    const semesterId = 1

    // 加载课程表数据
    const scheduleResponse = await fetch(`http://localhost:8080/api/schedule/user/${semesterId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    console.log('课程表API响应状态:', scheduleResponse.status)
    console.log('请求URL:', `http://localhost:8080/api/schedule/user/${semesterId}`)
    console.log('认证令牌:', token ? '已提供' : '未提供')

    if (scheduleResponse.ok) {
      const scheduleResult = await scheduleResponse.json()
      console.log('课程表API完整响应:', scheduleResult)
      console.log('响应数据类型:', typeof scheduleResult)
      console.log('响应是否成功:', scheduleResult.success)

      if (scheduleResult.code === 200 && scheduleResult.data) {
        // 处理课程表数据 - 直接从数据结构中获取
        const data = scheduleResult.data

        // 如果有 weeklySchedules 结构
        if (data && data.weeklySchedules && typeof data.weeklySchedules === 'object') {
          console.log('使用 weeklySchedules 数据结构，原始数据:', data.weeklySchedules)
          console.log('weeklySchedules 键:', Object.keys(data.weeklySchedules))

          // 检查是否为空对象
          if (Object.keys(data.weeklySchedules).length === 0) {
            console.warn('⚠️ weeklySchedules 是空对象，没有排课数据')
            scheduledCourses.value = []
          } else {
            Object.keys(data.weeklySchedules).forEach(dayKey => {
              const daySchedules = data.weeklySchedules[dayKey] || []
              console.log(`处理星期${dayKey}的课程，共${daySchedules.length}个:`, daySchedules)

            daySchedules.forEach((schedule, index) => {
              console.log(`处理星期${dayKey}第${index + 1}个课程:`, schedule)

              const timePeriodId = schedule.timePeriod?.id || schedule.timePeriodId || schedule.period

              if (!timePeriodId) {
                console.warn('weeklySchedules记录缺少时间段ID:', schedule)
                return
              }

              // 获取课程名称
              let courseName = '未知课程'
              if (schedule.course?.name) {
                courseName = schedule.course.name
              } else if (schedule.courseName) {
                courseName = schedule.courseName
              }

              // 获取教师名称
              let teacherName = ''
              if (schedule.course?.teacherName) {
                teacherName = schedule.course.teacherName
              } else if (schedule.teacherName) {
                teacherName = schedule.teacherName
              }

              // 获取教室名称
              let classroomName = ''
              if (schedule.classroom) {
                classroomName = `${schedule.classroom.building || ''}${schedule.classroom.roomNumber || ''}`
              } else if (schedule.classroomName) {
                classroomName = schedule.classroomName
              }

              const courseData = {
                id: schedule.id,
                courseId: schedule.courseId,
                classroomId: schedule.classroomId,
                courseName: courseName,
                teacherName: teacherName,
                teacherId: schedule.course?.teacherId || schedule.teacherId,
                classroomName: classroomName,
                day: parseInt(dayKey), // 确保是数字类型
                period: parseInt(timePeriodId), // 确保是数字类型
                dayOfWeek: parseInt(dayKey), // 兼容字段
                timePeriodId: parseInt(timePeriodId), // 兼容字段
                note: schedule.note || '',
                color: schedule.course?.color || schedule.color || '#4CAF50'
              }

              scheduledCourses.value.push(courseData)
              console.log('✅ 成功添加weeklySchedules课程到网格:', courseData)
            })
            })
          }
        }
        // 如果数据是直接的数组格式
        else if (Array.isArray(data)) {
          console.log('使用数组数据结构，数据条数:', data.length)
          data.forEach((schedule, index) => {
            console.log(`处理第${index + 1}个排课记录:`, schedule)

            // 确保数据完整性
            const dayOfWeek = schedule.dayOfWeek || schedule.day
            const timePeriodId = schedule.timePeriod?.id || schedule.timePeriodId || schedule.period

            if (!dayOfWeek || !timePeriodId) {
              console.warn('排课记录缺少关键字段:', { dayOfWeek, timePeriodId, schedule })
              return // 跳过不完整的记录
            }

            const courseData = {
              id: schedule.id,
              courseId: schedule.courseId,
              classroomId: schedule.classroomId,
              courseName: schedule.course?.name || schedule.courseName || '未知课程',
              teacherName: schedule.course?.teacherName || schedule.teacherName || '',
              teacherId: schedule.course?.teacherId || schedule.teacherId,
              classroomName: schedule.classroom ?
                `${schedule.classroom.building}-${schedule.classroom.roomNumber}` :
                schedule.classroomName || '',
              day: parseInt(dayOfWeek), // 确保是数字类型
              period: parseInt(timePeriodId), // 确保是数字类型
              note: schedule.note || '',
              color: schedule.course?.color || schedule.color || '#4CAF50'
            }

            scheduledCourses.value.push(courseData)
            console.log('成功添加课程到网格:', courseData)
          })
        }

        console.log('最终的scheduledCourses:', scheduledCourses.value)
        console.log('时间段定义:', timePeriods)
        console.log('星期定义:', weekDays)

        // 验证数据映射
        if (scheduledCourses.value.length > 0) {
          console.log('=== 数据映射验证 ===')
          scheduledCourses.value.forEach((course, index) => {
            console.log(`课程${index + 1}: ${course.courseName}`)
            console.log(`  -> 星期${course.day} (${weekDays.find(d => d.value === course.day)?.label || '未找到'})`)
            console.log(`  -> 时间段${course.period} (${timePeriods.find(p => p.value === course.period)?.label || '未找到'})`)
            console.log(`  -> 完整信息:`, course)
          })

          // 数据加载完成，强制刷新界面
          await nextTick()
          console.log('🔄 课程表数据加载完成，强制刷新界面')
          ElMessage.success(`课程表加载成功，共${scheduledCourses.value.length}门课程`)
        } else {
          console.log('⚠️ 没有找到任何排课数据')
          ElMessage.info('当前没有排课数据，请先进行自动排课')
        }
      }
    } else {
      const errorText = await scheduleResponse.text()
      console.error('课程表API请求失败:', {
        status: scheduleResponse.status,
        statusText: scheduleResponse.statusText,
        error: errorText
      })
      ElMessage.error(`获取课程表数据失败: ${scheduleResponse.status} ${scheduleResponse.statusText}`)
    }

    // 加载所有课程
    const coursesResponse = await fetch('http://localhost:8080/api/courses/list', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (coursesResponse.ok) {
      const coursesResult = await coursesResponse.json()
      if (coursesResult.code === 200) {
        allCourses.value = coursesResult.data || []
        console.log('加载的所有课程:', allCourses.value)

        // 计算未排课程
        const scheduledCourseIds = scheduledCourses.value.map(sc => sc.courseId)
        unscheduledCourses.value = allCourses.value.filter(course =>
          !scheduledCourseIds.includes(course.id)
        )
        console.log('未排课程:', unscheduledCourses.value)

        // 更新过滤后的课程列表
        updateFilteredCourses()
      }
    } else {
      console.log('课程API请求失败:', await coursesResponse.text())
    }

    // 加载教室数据
    const classroomsResponse = await fetch('http://localhost:8080/api/classrooms/list', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    console.log('教室API响应状态:', classroomsResponse.status)

    if (classroomsResponse.ok) {
      const classroomsResult = await classroomsResponse.json()
      console.log('教室数据响应:', classroomsResult)

      if (classroomsResult.code === 200) {
        availableClassrooms.value = classroomsResult.data || []
        console.log('加载的教室数据:', availableClassrooms.value)
      }
    } else {
      console.log('教室API请求失败:', await classroomsResponse.text())
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

// 其他操作
const clearSchedule = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有排课吗？', '确认清空', {
      type: 'warning'
    })

    const token = localStorage.getItem('admin_token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    // 获取当前学期ID（使用默认值1）
    const semesterId = 1

    const response = await fetch(`http://localhost:8080/api/schedule/clear?semesterId=${semesterId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    const result = await response.json()

    if (response.ok && result.code === 200) {
      ElMessage.success('排课已清空')
      await loadScheduleData() // 重新加载课程表数据
    } else {
      ElMessage.error(result.message || '清空失败')
    }
  } catch (error) {
    if (error.name !== 'cancel') {
      console.error('清空失败:', error)
      ElMessage.error('清空失败，请稍后重试')
    }
  }
}

const saveSchedule = async () => {
  saving.value = true
  try {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    // 验证教室必须选择
    if (!courseEditForm.classroomId) {
      ElMessage.error('请选择教室')
      return
    }

    // 获取当前编辑的课程安排数据
    const scheduleData = {
      courseId: courseEditForm.courseId,
      classroomId: courseEditForm.classroomId,
      semesterId: 1, // 使用当前学期ID
      timePeriodId: courseEditForm.period,
      dayOfWeek: courseEditForm.dayOfWeek,
      weeks: "1-18",
      note: courseEditForm.remarks
    }

    const response = await fetch('http://localhost:8080/api/schedule', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(scheduleData)
    })

    const result = await response.json()

    if (response.ok && result.code === 200) {
      ElMessage.success('排课保存成功')

      // 如果是编辑现有课程，先更新本地数据
      if (editingCourse.value) {
        const index = scheduledCourses.value.findIndex(sc =>
          sc.day === editingCourse.value.day && sc.period === editingCourse.value.period
        )
        if (index !== -1) {
          // 更新本地课程数据
          const classroom = availableClassrooms.value.find(c => c.id === scheduleData.classroomId)
          scheduledCourses.value[index] = {
            ...scheduledCourses.value[index],
            classroomId: scheduleData.classroomId,
            classroomName: classroom ? `${classroom.building}-${classroom.roomNumber}` : '',
            note: scheduleData.note
          }
        }
      }

      courseEditDialogVisible.value = false
      editingCourse.value = null

      // 重新加载数据以确保与后端同步
      await loadScheduleData()
    } else {
      ElMessage.error(result.message || '保存失败')
      console.error('保存失败详情:', result)
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败：网络错误')
  } finally {
    saving.value = false
  }
}

const exportSchedule = async () => {
  try {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    ElMessage.info('正在生成课程表...')

    // 获取当前学期ID（使用默认值1）
    const semesterId = 1

    const response = await fetch(`http://localhost:8080/api/schedule/export?semesterId=${semesterId}&format=xlsx`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.ok) {
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = `课程表_${new Date().toLocaleDateString()}.xlsx`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
      ElMessage.success('导出成功')
    } else {
      const errorText = await response.text()
      ElMessage.error(`导出失败: ${errorText}`)
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 冲突处理
const showConflictDetails = (day, period) => {
  currentConflicts.value = getCellConflicts(day, period)
  conflictDialogVisible.value = true
}

const resolveConflicts = () => {
  // TODO: 实现冲突自动解决
  ElMessage.info('冲突解决功能开发中...')
  conflictDialogVisible.value = false
}

// 课程编辑
const handleCellClick = (day, period) => {
  const course = getCellCourse(day, period)
  if (course) {
    editScheduledCourse(course)
  } else {
    // 新增课程安排
    editingCourse.value = null
    Object.assign(courseEditForm, {
      courseId: null,
      classroomId: null,
      dayOfWeek: day,
      period: period,
      remarks: ''
    })
    courseEditDialogVisible.value = true
  }
}

const editScheduledCourse = (course) => {
  console.log('编辑课程:', course)
  editingCourse.value = course
  Object.assign(courseEditForm, {
    courseId: course.courseId || course.id,
    classroomId: course.classroomId,
    dayOfWeek: course.day || course.dayOfWeek, // 修复字段名不一致
    period: course.period,
    remarks: course.remarks || course.note || ''
  })
  console.log('编辑表单数据:', courseEditForm)
  courseEditDialogVisible.value = true
}

const saveCurrentSchedule = async () => {
  saving.value = true
  try {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      ElMessage.error('请先登录')
      return
    }

    console.log('当前编辑表单数据:', courseEditForm)
    console.log('正在编辑的课程:', editingCourse.value)

    // 验证表单数据
    if (!courseEditForm.courseId || !courseEditForm.dayOfWeek || !courseEditForm.period) {
      ElMessage.error('请填写完整的课程安排信息')
      return
    }

    // 验证教室必须选择
    if (!courseEditForm.classroomId) {
      ElMessage.error('请选择教室')
      return
    }

    // 验证courseId是否存在于allCourses中
    const course = allCourses.value.find(c => c.id === courseEditForm.courseId)
    if (!course) {
      ElMessage.error('选择的课程不存在，请重新选择')
      console.error('课程ID不存在:', courseEditForm.courseId, '可用课程:', allCourses.value)
      return
    }

    // 获取当前编辑的课程安排数据
    const scheduleData = {
      courseId: parseInt(courseEditForm.courseId),
      classroomId: parseInt(courseEditForm.classroomId),
      semesterId: 1, // 当前学期ID
      timePeriodId: parseInt(courseEditForm.period),
      dayOfWeek: courseEditForm.dayOfWeek,
      weeks: "1-18",
      note: courseEditForm.remarks || null // 备注可以为null
    }

    console.log('发送到后端的数据:', scheduleData)

    let response
    let url = 'http://localhost:8080/api/schedule'
    let method = 'POST'

    // 如果是编辑现有课程，使用PUT方法
    if (editingCourse.value && editingCourse.value.id) {
      url = `http://localhost:8080/api/schedule/${editingCourse.value.id}`
      method = 'PUT'
      console.log('更新现有课程，ID:', editingCourse.value.id)
    } else {
      console.log('创建新课程')
    }

    response = await fetch(url, {
      method: method,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(scheduleData)
    })

    const result = await response.json()
    console.log('后端响应:', result)

    if (response.ok && result.code === 200) {
      ElMessage.success('排课保存成功')

      // 如果是编辑现有课程，先更新本地数据
      if (editingCourse.value) {
        const index = scheduledCourses.value.findIndex(sc =>
          sc.day === editingCourse.value.day && sc.period === editingCourse.value.period
        )
        if (index !== -1) {
          // 获取教室信息
          const classroom = availableClassrooms.value.find(c => c.id === scheduleData.classroomId)
          // 更新本地课程数据
          scheduledCourses.value[index] = {
            ...scheduledCourses.value[index],
            classroomId: scheduleData.classroomId,
            classroomName: classroom ? `${classroom.building}-${classroom.roomNumber}` : '',
            note: scheduleData.note
          }
          console.log('更新本地数据:', scheduledCourses.value[index])
        }
      }

      courseEditDialogVisible.value = false
      editingCourse.value = null

      // 重新加载数据以确保与后端同步
      await loadScheduleData()
    } else {
      ElMessage.error(result.message || '保存失败')
      console.error('保存失败详情:', result)
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败：网络错误')
  } finally {
    saving.value = false
  }
}

const handleSemesterChange = () => {
  loadScheduleData()
}

// 更新过滤后的课程列表
const updateFilteredCourses = () => {
  const keyword = courseSearchKeyword.value.toLowerCase()
  let filtered = unscheduledCourses.value

  // 如果有搜索关键词，进行过滤
  if (keyword) {
    filtered = unscheduledCourses.value.filter(course => {
      const courseNameMatch = course.name?.toLowerCase().includes(keyword)
      const courseTypeMatch = course.type?.toLowerCase().includes(keyword)
      const courseTeacherMatch = course.teacherName?.toLowerCase().includes(keyword)
      const courseCreditsMatch = course.credits?.toString().includes(keyword)
      return courseNameMatch || courseTypeMatch || courseTeacherMatch || courseCreditsMatch
    })
  }

  // 根据展开状态决定显示数量
  if (!expandCourseList.value && filtered.length > 6) {
    filteredCourses.value = filtered.slice(0, 6)
  } else {
    filteredCourses.value = filtered
  }
}

// 课程搜索
const filterCourses = () => {
  updateFilteredCourses()
}

// 课程列表展开/收起
const toggleCourseList = () => {
  expandCourseList.value = !expandCourseList.value
  updateFilteredCourses()
}

onMounted(async () => {
  await loadScheduleData()
  // 初始化过滤后的课程列表
  updateFilteredCourses()
})
</script>

<style scoped>
.schedules-container {
  height: 100vh;
  overflow: hidden;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 智能排课系统介绍 */
.schedule-intro {
  background-color: #f0f8ff; /* 浅蓝色背景 */
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  text-align: center;
  color: #303133;
  font-size: 16px;
  line-height: 1.6;
}

.schedule-intro h1 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 700;
  color: #409eff; /* 主题色 */
}

.schedule-intro p {
  margin-bottom: 0;
  color: #606266;
}

/* 控制卡片 */
.control-card {
  margin-bottom: 20px;
}

.course-list-card {
  margin-bottom: 16px;
}

.compact-card {
  margin-bottom: 12px;
}

.compact-card .el-card__body {
  padding: 12px;
}

.compact-card .el-card__header {
  padding: 12px 16px;
}

.control-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-card h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

/* 课程搜索 */
.course-search {
  margin-bottom: 12px;
}

.course-search .el-input {
  width: 100%;
}

/* 待排课程 */
.course-list {
  max-height: 120px; /* 大幅减少高度，确保统计组件完全可见 */
  overflow-y: auto;
  padding-right: 4px; /* 为滚动条留出空间 */
}

/* 自定义滚动条 */
.course-list::-webkit-scrollbar {
  width: 6px;
}

.course-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.course-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.course-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.empty-search,
.empty-courses {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-search .el-icon,
.empty-courses .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.course-list-footer {
  text-align: center;
  padding: 8px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 8px;
}

.course-item {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.2s ease;
  background: #fff;
}

.course-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.course-item.dragging {
  opacity: 0.5;
  cursor: grabbing;
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.course-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.course-info {
  display: flex;
  gap: 12px;
  margin-bottom: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.course-requirements {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #909399;
}

.empty-courses {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-courses .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #67c23a;
}



/* 排课统计 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.stats-card :deep(.el-card__header) {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px 20px;
}

.stats-card :deep(.el-card__body) {
  padding: 20px;
}

.stats-header {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.stats-icon {
  font-size: 24px;
  color: #fff;
}

.stats-header h3 {
  margin: 0;
  color: white;
  font-weight: 600;
}

.schedule-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.total-courses .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.scheduled-courses .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.conflict-courses .stat-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stat-content.full-width {
  width: 100%;
}

.completion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-value.scheduled {
  color: #43e97b;
}

.stat-value.conflict {
  color: #fa709a;
}

/* 详细统计 */
.detailed-stats {
  margin-top: 20px;
}

.detailed-stats :deep(.el-divider__text) {
  color: rgba(255, 255, 255, 0.9);
  background: transparent;
}

.detailed-stats :deep(.el-divider) {
  border-color: rgba(255, 255, 255, 0.2);
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

/* 课表网格 */
.schedule-card {
  height: calc(100vh - 200px);
  overflow: hidden;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.schedule-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.schedule-controls {
  display: flex;
  gap: 16px;
}

.schedule-grid {
  height: calc(100% - 60px);
  overflow: auto;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.grid-header {
  display: grid;
  grid-template-columns: 100px repeat(7, 1fr);
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  position: sticky;
  top: 0;
  z-index: 10;
}

.time-header,
.day-header {
  padding: 12px 8px;
  text-align: center;
  font-weight: 500;
  border-right: 1px solid #e4e7ed;
}

.day-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.day-name {
  font-size: 14px;
  color: #303133;
}

.day-date {
  font-size: 12px;
  color: #909399;
}

.grid-body {
  min-height: 100%;
}

.grid-row {
  display: grid;
  grid-template-columns: 100px repeat(7, 1fr);
  border-bottom: 1px solid #e4e7ed;
}

.time-cell {
  padding: 8px;
  background: #fafafa;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.time-slot {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
}

.time-range {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
}

.schedule-cell {
  min-height: 80px;
  border-right: 1px solid #e4e7ed;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.schedule-cell:hover {
  background-color: #f0f8ff;
}

.schedule-cell.has-course {
  padding: 4px;
}

.schedule-cell.has-conflict {
  background-color: #fef0f0;
}

.schedule-cell.can-drop {
  background-color: #f0f9ff;
  border: 2px dashed #409eff;
}

.schedule-cell.cannot-drop {
  background-color: #fef0f0;
  border: 2px dashed #f56c6c;
}

/* 课程卡片 */
.course-card {
  height: 100%;
  padding: 8px;
  border-radius: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 12px;
  position: relative;
  overflow: hidden;
}

.course-card.conflict {
  background: linear-gradient(135deg, #f56c6c 0%, #ff6b9d 100%);
}

.course-name {
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.2;
}

.course-teacher,
.course-classroom {
  font-size: 11px;
  opacity: 0.9;
  margin-bottom: 2px;
}

.course-note {
  font-size: 10px;
  color: #e0e0e0;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-actions {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.course-card:hover .course-actions {
  opacity: 1;
}

.course-actions .el-button {
  width: 20px;
  height: 20px;
  padding: 0;
  margin-left: 2px;
}

/* 冲突指示器 */
.conflict-indicator {
  position: absolute;
  bottom: 4px;
  left: 4px;
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px 4px;
  background: rgba(245, 108, 108, 0.9);
  color: white;
  font-size: 10px;
  border-radius: 2px;
  cursor: pointer;
}

/* 拖拽指示器 */
.drop-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.drop-indicator.can-drop {
  color: #67c23a;
}

.drop-indicator.cannot-drop {
  color: #f56c6c;
}

/* 冲突详情 */
.conflict-details {
  padding: 16px 0;
}

.conflict-list {
  margin-top: 16px;
}

.conflict-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
}

.conflict-type {
  margin-bottom: 8px;
}

.conflict-description {
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
}

.conflict-courses {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.conflict-course {
  font-size: 12px;
  color: #606266;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 响应式 */
@media (max-width: 1200px) {
  .grid-header,
  .grid-row {
    grid-template-columns: 80px repeat(7, 1fr);
  }

  .time-cell {
    padding: 6px 4px;
  }

  .course-card {
    padding: 6px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    flex-wrap: wrap;
  }

  .schedule-controls {
    flex-direction: column;
    gap: 8px;
  }

  .grid-header,
  .grid-row {
    grid-template-columns: 60px repeat(5, 1fr);
  }

  .day-header:nth-child(n+7) {
    display: none;
  }

  .schedule-cell:nth-child(n+7) {
    display: none;
  }
}
</style>
