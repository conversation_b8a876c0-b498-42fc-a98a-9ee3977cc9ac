{"version": 3, "file": "total.js", "sources": ["../../../../../../../packages/components/pagination/src/components/total.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type Total from './total.vue'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const paginationTotalProps = buildProps({\n  total: {\n    type: Number,\n    default: 1000,\n  },\n} as const)\n\nexport type PaginationTotalProps = ExtractPropTypes<typeof paginationTotalProps>\nexport type PaginationTotalPropsPublic = __ExtractPublicPropTypes<\n  typeof paginationTotalProps\n>\n\nexport type TotalInstance = InstanceType<typeof Total> & unknown\n"], "names": ["buildProps"], "mappings": ";;;;;;AACY,MAAC,oBAAoB,GAAGA,kBAAU,CAAC;AAC/C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,CAAC;;;;"}